<?php

namespace Database\Seeders;

use App\Models\Amenity;
use Illuminate\Database\Seeder;

class AmenitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $amenities = [
            [
                'name' => 'Lighting',
                'description' => 'Professional LED lighting system for evening games and events',
                'icon_class' => 'ri-lightbulb-line',
                'is_active' => true,
            ],
            [
                'name' => 'Parking',
                'description' => 'Dedicated parking area for players and spectators',
                'icon_class' => 'ri-car-line',
                'is_active' => true,
            ],
            [
                'name' => 'Restrooms',
                'description' => 'Clean and accessible restroom facilities',
                'icon_class' => 'ri-home-line',
                'is_active' => true,
            ],
            [
                'name' => 'Equipment Available',
                'description' => 'Sports equipment available for rent or use',
                'icon_class' => 'ri-archive-line',
                'is_active' => true,
            ],
            [
                'name' => 'Scoreboard',
                'description' => 'Digital scoreboard for game tracking and display',
                'icon_class' => 'ri-dashboard-line',
                'is_active' => true,
            ],
            [
                'name' => 'Spectator Seating',
                'description' => 'Comfortable seating for spectators and fans',
                'icon_class' => 'ri-armchair-line',
                'is_active' => true,
            ],
            [
                'name' => 'Sound System',
                'description' => 'Professional audio system for announcements and music',
                'icon_class' => 'ri-volume-up-line',
                'is_active' => true,
            ],
            [
                'name' => 'WiFi',
                'description' => 'High-speed wireless internet access',
                'icon_class' => 'ri-wifi-line',
                'is_active' => true,
            ],
            [
                'name' => 'Concessions',
                'description' => 'Food and beverage concession stand',
                'icon_class' => 'ri-restaurant-line',
                'is_active' => true,
            ],
            [
                'name' => 'Lockers',
                'description' => 'Secure locker facilities for players',
                'icon_class' => 'ri-lock-line',
                'is_active' => true,
            ],
            [
                'name' => 'Showers',
                'description' => 'Clean shower facilities for players',
                'icon_class' => 'ri-drop-line',
                'is_active' => true,
            ],
            [
                'name' => 'First Aid Station',
                'description' => 'Emergency first aid and medical supplies',
                'icon_class' => 'ri-heart-pulse-line',
                'is_active' => true,
            ],
        ];

        foreach ($amenities as $amenityData) {
            Amenity::firstOrCreate(
                ['name' => $amenityData['name']],
                $amenityData
            );
        }

        $this->command->info('Amenities seeded successfully!');
    }
}
