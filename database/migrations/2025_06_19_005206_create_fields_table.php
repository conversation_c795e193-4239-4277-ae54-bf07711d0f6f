<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['Soccer', 'Basketball', 'Tennis', 'Volleyball', 'Baseball', 'Football', 'Multi-Purpose']);
            $table->text('description')->nullable();
            $table->decimal('hourly_rate', 8, 2);
            $table->integer('capacity');
            $table->enum('status', ['Active', 'Inactive', 'Under Maintenance'])->default('Active');
            $table->json('amenities')->nullable(); // Store amenities as JSON array
            $table->softDeletes();
            $table->timestamps();

            // Indexes for performance
            $table->index(['status']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
