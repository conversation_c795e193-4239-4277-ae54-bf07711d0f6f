<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role column with enum values
            $table->enum('role', ['admin', 'normal_user', 'client'])->default('normal_user')->after('email');

            // Add username column for alternative login
            $table->string('username')->unique()->nullable()->after('name');

            // Add failed login attempts tracking
            $table->integer('failed_login_attempts')->default(0)->after('password');
            $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');

            // Add index for performance
            $table->index(['role']);
            $table->index(['username']);
            $table->index(['failed_login_attempts']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role']);
            $table->dropIndex(['username']);
            $table->dropIndex(['failed_login_attempts']);
            $table->dropColumn(['role', 'username', 'failed_login_attempts', 'locked_until']);
        });
    }
};
