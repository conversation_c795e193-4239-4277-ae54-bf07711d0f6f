<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we need to recreate the table with new enum values
        if (DB::getDriverName() === 'sqlite') {
            // Create a temporary table with new structure
            Schema::create('users_temp', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email')->unique();
                $table->string('username')->unique()->nullable();
                $table->enum('role', ['admin', 'employee', 'user'])->default('employee');
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->integer('failed_login_attempts')->default(0);
                $table->timestamp('locked_until')->nullable();
                $table->rememberToken();
                $table->timestamps();

                $table->index(['role']);
                $table->index(['username']);
                $table->index(['failed_login_attempts']);
            });

            // Copy data with role mapping
            DB::statement("
                INSERT INTO users_temp (id, name, email, username, role, email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at)
                SELECT
                    id,
                    name,
                    email,
                    username,
                    CASE
                        WHEN role = 'normal_user' THEN 'employee'
                        WHEN role = 'client' THEN 'user'
                        ELSE role
                    END as role,
                    email_verified_at,
                    password,
                    failed_login_attempts,
                    locked_until,
                    remember_token,
                    created_at,
                    updated_at
                FROM users
            ");

            // Drop old table and rename temp table
            Schema::drop('users');
            Schema::rename('users_temp', 'users');
        } else {
            // For MySQL/PostgreSQL
            DB::table('users')->where('role', 'normal_user')->update(['role' => 'employee']);
            DB::table('users')->where('role', 'client')->update(['role' => 'user']);
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'employee', 'user') NOT NULL DEFAULT 'employee'");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For SQLite, recreate table with old enum values
        if (DB::getDriverName() === 'sqlite') {
            Schema::create('users_temp', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('email')->unique();
                $table->string('username')->unique()->nullable();
                $table->enum('role', ['admin', 'normal_user', 'client'])->default('normal_user');
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->integer('failed_login_attempts')->default(0);
                $table->timestamp('locked_until')->nullable();
                $table->rememberToken();
                $table->timestamps();

                $table->index(['role']);
                $table->index(['username']);
                $table->index(['failed_login_attempts']);
            });

            // Copy data with role mapping back
            DB::statement("
                INSERT INTO users_temp (id, name, email, username, role, email_verified_at, password, failed_login_attempts, locked_until, remember_token, created_at, updated_at)
                SELECT
                    id,
                    name,
                    email,
                    username,
                    CASE
                        WHEN role = 'employee' THEN 'normal_user'
                        WHEN role = 'user' THEN 'client'
                        ELSE role
                    END as role,
                    email_verified_at,
                    password,
                    failed_login_attempts,
                    locked_until,
                    remember_token,
                    created_at,
                    updated_at
                FROM users
            ");

            Schema::drop('users');
            Schema::rename('users_temp', 'users');
        } else {
            // For MySQL/PostgreSQL
            DB::table('users')->where('role', 'employee')->update(['role' => 'normal_user']);
            DB::table('users')->where('role', 'user')->update(['role' => 'client']);
            DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'normal_user', 'client') NOT NULL DEFAULT 'normal_user'");
        }
    }
};
