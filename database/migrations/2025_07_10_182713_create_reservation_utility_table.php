<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reservation_utility', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reservation_id')->constrained('bookings')->onDelete('cascade');
            $table->foreignId('utility_id')->constrained()->onDelete('cascade');
            $table->unsignedInteger('hours');
            $table->decimal('rate', 8, 2);
            $table->decimal('cost', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reservation_utility');
    }
};
