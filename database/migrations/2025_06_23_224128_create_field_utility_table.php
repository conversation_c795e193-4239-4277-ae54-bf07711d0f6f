<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('field_utility', function (Blueprint $table) {
            $table->id();
            $table->foreignId('field_id')->constrained()->onDelete('cascade');
            $table->foreignId('utility_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            // Ensure unique field-utility combinations
            $table->unique(['field_id', 'utility_id']);

            // Indexes for better performance
            $table->index('field_id');
            $table->index('utility_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_utility');
    }
};
